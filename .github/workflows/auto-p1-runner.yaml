name: Auto P1 Load Test Runner

on:
  workflow_dispatch:

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  GKE_CLUSTER: ${{ secrets.GKE_CLUSTER }}    
  GKE_ZONE: us-central1-a   

jobs:
  run-load-test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test: 
          - "dealssmartlist"
          - "dealssmartlistwarehousecategories"
          - "homepagenearestaddress"
          - "homepageconfig"
          - "homepagereferral"
          - "autocomplete"
          - "listcoupons"
          - "getordershistory"
          - "getuserdata"
          - "updateuser"
          - "transit"
          
      max-parallel: 2

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up kubectl
      uses: azure/setup-kubectl@v4
      with:
        version: 'latest'

    - name: Auth
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}

    - name: Get GKE Credentials
      uses: google-github-actions/get-gke-credentials@v2
      with:
        location: ${{ secrets.GKE_ZONE }}
        cluster_name: ${{ env.GKE_CLUSTER }}

    - name: Set scenario as env
      run: echo "TEST_SCENARIO=${{ matrix.test }}" >> $GITHUB_ENV

    - name: Clean Old Runs
      run: |
        kubectl delete -f runners/k6-${{ matrix.test }}.yaml --ignore-not-found=true -n k6-operator-system

    - name: Sleep for 30 seconds
      run: sleep 30s
      shell: bash

    - name: Prepare Test
      run: |
        kubectl create configmap $TEST_SCENARIO --from-file=tests/test-${{ matrix.test }}.js --dry-run -o yaml | kubectl apply -n k6-operator-system -f -

    - name: Apply Kubernetes Manifests
      run: |
        kubectl apply -f runners/k6-${{ matrix.test }}.yaml -n k6-operator-system
        sleep 1560s
