name: Run Load Test
on:
  workflow_dispatch:
    inputs:
      test: 
        description: 'Select Test Case to Run'
        type: choice
        required: true
        options:
          - "orderfail"
          - "coupons"
          - "checkout"
          - "paymentcaptureorder"
          - "paymentcustomerallpaymentmethodswithmethodid"
          - "paymentgetorderstatus"
          - "paymentgetorderdetail"
          - "paymentcancelorder"
          - "paymentpayorder"
          - "paymentappconfig"
          - "paymentcustomerallpaymentmethodswithcardid"
          - "paymentcustomerallpaymentmethodswithid"
          - "paymentcustomerallpaymentmethods"
          - "getcoupondata"
          - "checkoutpaymentuserdata"
          - "getavailableslots"
          - "checkoutproducts"
          - "favorites"
          - "cashback"
          - "dealssmartlist"
          - "dealssmartlistwarehousecategories"
          - "homepagenearestaddress"
          - "homepageconfig"
          - "homepagereferral"
          - "login"
          - "search"
          - "search-new-integration"
          - "autocomplete"
          - "updateaddress"
          - "updateaddress-new-integration"
          - "defaultaddress"
          - "deleteaddress"
          - "listcoupons"
          - "getorders"
          - "getordershistory"
          - "availability"
          - "availability-new-integration"
          - "createorder"
          - "checkorders"
          - "getuserdata"
          - "updateuser"
          - "sendotp"
          - "transit"
          - "croomlistorders"
          - "savedpaymentmethods"
          - "savedpaymentmethoddetails"
          - "customerpaymentmethoddetails"
          - "paymentgetcarddetails"
          - "appconfig"
          - "paymentorderdetails"
          - "paymentorderstatus"
          - "paymentordercreatepayrollback"
          - "newcreateorder"
env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  GKE_CLUSTER: ${{ secrets.GKE_CLUSTER }}    
  GKE_ZONE: us-central1-a   
jobs:
  run-load-test:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Set up kubectl
      uses: azure/setup-kubectl@v1
      with:
        version: 'latest'

    - name: Auth
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}

    - name: Get GKE Credentials
      uses: google-github-actions/get-gke-credentials@db150f2cc60d1716e61922b832eae71d2a45938f
      with:
        location: ${{ secrets.GKE_ZONE }}
        cluster_name: ${{ env.GKE_CLUSTER }}

    - name: Set scenarion as env
      run: echo "TEST_SCENARIO=${{ github.event.inputs.test }}" >> $GITHUB_ENV

    - name: Clean Old Runs
      run: |
        kubectl delete -f runners/k6-${{ github.event.inputs.test }}.yaml --ignore-not-found=true -n k6-operator-system 
    - name: Sleep for 30 seconds
      run: sleep 30s
      shell: bash

    - name: Prepare Test
      run: |
        kubectl create configmap $TEST_SCENARIO --from-file=tests/test-${{ github.event.inputs.test }}.js --dry-run -o yaml | kubectl apply -n k6-operator-system -f - 
    - name: Apply Kubernetes Manifests
      run: |
        kubectl apply -f runners/k6-${{ github.event.inputs.test }}.yaml -n k6-operator-system 

    - name: Sleep for 2 minutes
      run: sleep 2m
      shell: bash

    - name: List all jobs and pods
      run: |
        kubectl get jobs -n k6-operator-system
        kubectl get pods -n k6-operator-system

    - name: Fetch and wait for all pods to complete
      run: |
        POD_NAMES=$(kubectl get pods -n k6-operator-system -l job-name=k6-${{ github.event.inputs.test }} -o jsonpath="{.items[*].metadata.name}")
        for pod in $POD_NAMES; do
          kubectl wait --for=condition=complete $pod -n k6-operator-system --timeout=600s
        done

    - name: Capture logs from all k6 pods
      run: |
        POD_NAMES=$(kubectl get pods -n k6-operator-system -l job-name=k6-${{ github.event.inputs.test }} -o jsonpath="{.items[*].metadata.name}")
        for pod in $POD_NAMES; do
          echo "Fetching logs for pod: $pod"
          kubectl logs $pod -n k6-operator-system | tee -a "$pod-log.txt"
        done

    - name: Print logs for debugging
      run: |
        for pod in $POD_NAMES; do
          echo "Printing logs from $pod"
          cat "logs/$pod-log.txt"
        done

    - name: Upload logs as artifact
      uses: actions/upload-artifact@v4
      with:
        name: k6-logs
        path: logs/
