apiVersion: k6.io/v1alpha1
kind: K6
metadata:
  name: k6-availability
  namespace: k6-operator-system
spec:
  parallelism: 6
  arguments: --out statsd --tag testcase=availability
  script:
    configMap:
      name: availability
      file: test-availability.js
  runner:
    image: ghcr.io/grafana/k6-operator:runner-v0.0.17
    resources:
      limits:
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 256Mi
    env:
    - name: K6_STATSD_ENABLE_TAGS
      value: "true"
    - name: K6_STATSD_ADDR
      value: datadog-agent.monitoring.svc.cluster.local:8125
    - name: K6_STATSD_TAG_BLOCKLIST
      value: vu,iter
