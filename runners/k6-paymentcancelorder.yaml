apiVersion: k6.io/v1alpha1
kind: K6
metadata:
  name: k6-paymentcancelorder
  namespace: k6-operator-system
spec:
  parallelism: 10
  arguments: --out statsd --tag testcase=paymentcancelorder
  script:
    configMap:
      name: paymentcancelorder
      file: test-paymentcancelorder.js
  runner:
    resources:
      limits:
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 256Mi
    env:
    - name: K6_STATSD_ENABLE_TAGS
      value: "true"
    - name: K6_STATSD_ADDR
      value: datadog-agent.monitoring.svc.cluster.local:8125
    - name: K6_STATSD_TAG_BLOCKLIST
      value: vu,iter
