apiVersion: k6.io/v1alpha1
kind: K6
metadata:
  name: k6-paymentordercreatepayrollback
  namespace: k6-operator-system
spec:
  parallelism: 6
  arguments: --out statsd --tag testcase=paymentordercreatepayrollback
  script:
    configMap:
      name: paymentordercreatepayrollback
      file: test-paymentordercreatepayrollback.js
  runner:
    resources:
      limits:
        memory: 1Gi
      requests:
        cpu: 20m
        memory: 50Mi
    env:
    - name: K6_STATSD_ENABLE_TAGS
      value: "true"
    - name: K6_STATSD_ADDR
      value: datadog-agent.monitoring.svc.cluster.local:8125
    - name: K6_STATSD_TAG_BLOCKLIST
      value: vu,iter
