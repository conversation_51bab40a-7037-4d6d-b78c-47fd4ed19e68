apiVersion: k6.io/v1alpha1
kind: K6
metadata:
  name: k6-getcoupondata
  namespace: k6-operator-system
spec:
  parallelism: 6
  arguments: --out statsd --tag testcase=getcoupondata
  script:
    configMap:
      name: getcoupondata
      file: test-getcoupondata.js
  runner:
    resources:
      limits:
        memory: 4Gi
      requests:
        cpu: 200m
        memory: 1Gi
    env:
    - name: K6_STATSD_ENABLE_TAGS
      value: "true"
    - name: K6_STATSD_ADDR
      value: datadog-agent.monitoring.svc.cluster.local:8125
    - name: K6_STATSD_TAG_BLOCKLIST
      value: vu,iter
