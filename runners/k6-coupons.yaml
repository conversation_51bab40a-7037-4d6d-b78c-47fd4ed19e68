apiVersion: k6.io/v1alpha1
kind: K6
metadata:
  name: k6-coupons
  namespace: k6-operator-system
spec:
  parallelism: 8
  arguments: --out statsd --tag testcase=coupons
  script:
    configMap:
      name: coupons
      file: test-coupons.js
  runner:
    resources:
      limits:
        memory: 4Gi
      requests:
        cpu: 200m
        memory: 1Gi
    env:
    - name: K6_STATSD_ENABLE_TAGS
      value: "true"
    - name: K6_STATSD_ADDR
      value: datadog-agent.monitoring.svc.cluster.local:8125
    - name: K6_STATSD_TAG_BLOCKLIST
      value: vu,iter
