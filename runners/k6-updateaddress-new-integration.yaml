apiVersion: k6.io/v1alpha1
kind: K6
metadata:
  name: k6-updateaddress-new-integration
  namespace: k6-operator-system
spec:
  parallelism: 8
  arguments: --out statsd --tag testcase=updateaddress-new-integration
  script:
    configMap:
      name: updateaddress-new-integration
      file: test-updateaddress-new-integration.js
  runner:
    resources:
      limits:
        memory: 1Gi
      requests:
        cpu: 20m
        memory: 100Mi
    env:
    - name: K6_STATSD_ENABLE_TAGS
      value: "true"
    - name: K6_STATSD_ADDR
      value: datadog-agent.monitoring.svc.cluster.local:8125
    - name: K6_STATSD_TAG_BLOCKLIST
      value: vu,iter
