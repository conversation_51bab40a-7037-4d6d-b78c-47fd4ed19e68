import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 25, duration: '60s' },
    { target: 75, duration: '120s' },
    { target: 50, duration: '180s' },
    { target: 100, duration: '600s' },
    { target: 100, duration: '120s' },
    { target: 50, duration: '60s' },
  ],
};

export default function() {
  const url = getEndpoint()
  const response = http.get(url, { timeout: "120s" });

  // Check if the request was successful (status code 2xx)
  check(response, {
    'Status is 200': (r) => r.status === 200,
  });
}

function getEndpoint() {
  const endpoints = [
    "https://new-integration.breadfast.tech/warehouses-service/warehouses/availability/?lat=29.99856113215991&lng=31.41396911814809&warehouseId=5e36306926b8d24cc44516c3"
  ]
  const index = Math.floor(Math.random() * endpoints.length);
  return endpoints[index]
}
