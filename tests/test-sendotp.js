import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 5, duration: '30s' },
    { target: 10, duration: '60s' },
    { target: 25, duration: '60s' },
    { target: 50, duration: '60s' },
    { target: 100, duration: '240s' },
    { target: 200, duration: '200s' },
    { target: 50, duration: '60s' },
    { target: 30, duration: '120s' },
    { target: 5, duration: '60s' },
  ],
};


export default function() {
  const url = "https://integration.breadfast.tech/wp-json/breadfast/v4/user/send-otp";

  const headers = {
    'Content-Type': 'application/json',
    'recaptchaEnabled': "true",
    "g-recaptcha-response": "03AFY_a8VRESJ7q3R7upAjG8DklRgWs_cx8qBjVUTcsuGm5yMxiEcLYG58ih9z512UDI_g116mulXOw8Q-Z-YqRreMEGCEIjaqMSthtzp_ey13alEkd0f7i-DegWLVLfhvLJCDerSpji-sj1N0VJO4BCpw8WMFAP2_MO_OK7KVwC9GjWBDT09U7PGA1ZPQoyoOivI_1MPbHJm6_bLonmaslM1KpbwxtYr_5E6wG5T2ZyPgNhyqjaPl7mqz_Y_56fJpcWrjcyv-HtExcc3rPnnFEPBeYmKo3IOFqWTXufeKdJGeKbOrtojhX4u1dN1fhVvhEsQp2J6sppWzrNgB_S0hbqdgT49ZuCS6lsAEo2enVJm9I-LECFSe8lJvcG_7QsdWtYJlVPDDCr3-3iAQvB1NVvsgSMmvW-fvVEnLoMCcEFw6g0whI5-n5di0RoMkDeASmJlrU141FTxXsFmaMCi8LHoKIb6ybJwLi1bozjCS7j8WEF97jUqnZPCw3ovqxHr012tx6GoymQX2"
  };
var ran = Math.floor(Math.random() * 10000)
  const payload = {
  "country_code": "EG",
  "phone": "0103011" + ran
};

  const response = http.post(url, JSON.stringify(payload), { headers: headers, timeout: "120s" });

  // Check if the request was successful (status code 2xx)
  check(response, {
    'Status is 200': (r) => JSON.parse(r.body).status === 200,
  });
}
