import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 5, duration: '30s' },
    { target: 10, duration: '60s' },
    { target: 25, duration: '60s' },
    { target: 60, duration: '180s' },
    { target: 100, duration: '240s' },
    { target: 200, duration: '300s' },
    { target: 50, duration: '60s' },
    { target: 30, duration: '120s' },
    { target: 5, duration: '60s' },
  ],
};
export default function() {
  const url = getEndpoint()
  const response = http.get(url, { timeout: "120s" });

  // Check if the request was successful (status code 2xx)
  check(response, {
    'Status is 200': (r) => r.status === 200,
  });
}

function getEndpoint() {
  const endpoints = [
    "https://integration.breadfast.tech/warehouses-service/warehouses/products?validate=true&serve=now&shiftId=5fc84b891758b02853efc357&warehouseId=5e36306926b8d24cc44516c3&ids%5B0%5D=26529313:1&source=cart"
];  const index = Math.floor(Math.random() * endpoints.length);
  return endpoints[index]
}
