import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 5, duration: '30s' },
    { target: 10, duration: '60s' },
    { target: 25, duration: '60s' },
    { target: 60, duration: '180s' },
    { target: 100, duration: '240s' },
    { target: 200, duration: '300s' },
    { target: 50, duration: '60s' },
    { target: 30, duration: '120s' },
    { target: 5, duration: '60s' },
  ],
};

export default function() {
  const url = "https://payment-integration.breadfast.tech/api/app/config";

  const headers = {
    'Accept': 'application/json',
    'key': '$2x$bf$.bx5Dh353yA9PQDdFvg4J2UNgvTp3qmxraMf3nT3mW87vVHMg2KXT7',
    'secret': '$0ibd$i9WfIPMqbtSaGrZuKwetJSiGup9kMy4akAwZas7lBt0Xk4zU97uBr.qSjJXW',
  };

  const response = http.get(url, { headers: headers, timeout: "120s" });

  // Check if the HTTP status code is 200 and the parsed status is also 200
  check(response, {
    'Status is 200': (r) => r.status === 200,
  });

}
