import http from "k6/http";
import { check } from "k6";

const baseUrl = "https://card-backend-preprod.breadfast.tech/api/v1";

export const options = {
  stages: [
    { target: 5, duration: "30s" },
    { target: 10, duration: "60s" },
    { target: 25, duration: "60s" },
    { target: 60, duration: "200s" },
    { target: 150, duration: "300s" },
    { target: 250, duration: "420s" },
    { target: 50, duration: "80s" },
    { target: 30, duration: "140s" },
    { target: 5, duration: "60s" },
  ],
};

const getOnboardingData = async (token) => {
  const response = await http.post(
    `${baseUrl}/mobile/cards/staticFeatures/list`,
    {},
    {
      headers: {
        Authorization: token,
        "Content-Type": "application/json",
      },
    }
  );
  try {
    const responseData = response.json();

    check(response, {
      "Response status is 200": (r) => r.status === 200,
      "Returns static data": (r) => Array.isArray(responseData.data),
    });
  } catch (error) {
    console.error(`Failed to get static features list: ${error.message}`);
  }
};

export default function () {
  const mobileToken =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EZLWm1hxBtew8DZ78DldyG5eJ6q1TCtCWgaRKzynjic";
  try {
    getOnboardingData(mobileToken);
    check(null, {
      "Static Features list is not empty": () => true,
    });
  } catch (error) {
    console.error(`Failed to get static features list: ${error.message}`);
    check(null, {
      "Static Features list is not empty": () => false,
    });
  }
}
