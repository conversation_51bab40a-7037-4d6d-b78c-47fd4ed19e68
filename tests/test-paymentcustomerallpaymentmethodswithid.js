import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 5, duration: '30s' },
    { target: 10, duration: '60s' },
    { target: 25, duration: '60s' },
    { target: 60, duration: '180s' },
    { target: 100, duration: '240s' },
    { target: 200, duration: '300s' },
    { target: 50, duration: '60s' },
    { target: 30, duration: '120s' },
    { target: 5, duration: '60s' },
  ],
};


export default function() {
  const url = "https://payment-integration.breadfast.tech/api/customer/saved-payment-methods/5";

  const headers = {
    'Content-Type': 'application/json',
    "Authorization": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvbmV3LXRlc3RpbmcuYnJlYWRmYXN0LmFwcCIsImlhdCI6MTY1NTIxMjkyOSwibmJmIjoxNjU1MjEyOTI5LCJleHAiOjE3NDE2MTI5MjksImRhdGEiOnsidXNlciI6eyJpZCI6Ijc1MjUiLCJyb2xlcyI6WyJhZG1pbmlzdHJhdG9yIl19fX0.9GZCCGD_GuR3oP87BJfZyHyUzBk8a-ril9QusfE67dA"
  };
  
  const payload = {}

  const response = http.get(url, JSON.stringify(payload), { headers: headers, timeout: "120s" });

 // Parse the response JSON if possible
  let responseData;
  try {
    responseData = response.json();
  } catch (e) {
    console.error("Failed to parse JSON response:", e);
  }

  // Check if the HTTP status code is 200 and the parsed status is also 200
  check(response, {
    'Status is 200': (r) => r.status === 200,
  });

  if (responseData) {
    check(responseData, {
      'Response status is 200': (data) => data.status === 200,
    });
  }
}
