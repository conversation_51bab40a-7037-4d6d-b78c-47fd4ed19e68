import http from "k6/http";
import { check } from "k6";

const baseUrl = "https://card-backend-preprod.breadfast.tech/api/v1";

export const options = {
  stages: [
    { target: 5, duration: "30s" },
    { target: 10, duration: "60s" },
    { target: 25, duration: "60s" },
    { target: 60, duration: "200s" },
    { target: 150, duration: "300s" },
    { target: 250, duration: "420s" },
    { target: 50, duration: "80s" },
    { target: 30, duration: "140s" },
    { target: 5, duration: "60s" },
  ],
};

const validateCardStatus = async (webToken) => {
  const expectedStatus = "Pending";
  const requestData = { wallet_id: "+201000001246" };

  const response = await http.post(
    `${baseUrl}/web/cards/status`,
    JSON.stringify(requestData),
    {
      headers: {
        Authorization: webToken,
        "Content-Type": "application/json",
      },
    }
  );
  try {
    const responseData = response.json();
    check(response, {
      "Response status is 200": (r) => r.status === 200,
      "Status matches expected": (r) =>
        responseData.data.status === expectedStatus,
    });
  } catch (error) {
    console.error(
      `Failed to validate card status for user with breadfast_id 1416181: ${error.message}`
    );
  }
};

export default function () {
  const webToken =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.70MsJffZ9pFA385m5Xg2xQ9r484QYjZTXoTc16OC7H8";

  try {
    validateCardStatus(webToken);
    check(null, {
      "Card status validation successful": () => true,
    });
  } catch (error) {
    console.error(
      `Failed to validate card status for user with breadfast_id 1416181: ${error.message}`
    );
    check(null, {
      "Card status validation successful": () => false,
    });
  }
}
