import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 5, duration: '30s' },
    { target: 10, duration: '60s' },
    { target: 25, duration: '60s' },
    { target: 50, duration: '30s' },
    { target: 75, duration: '100s' },
    { target: 155, duration: '180s' },
    { target: 300, duration: '300s' },
    { target: 50, duration: '60s' },
    { target: 25, duration: '30s' },
  ],
};

export default function() {
  const url = "https://payment-integration.breadfast.tech/api/customer/847066/payment-methods/3c5d8c48-97c2-43f2-a53f-9893dc5de41d";

  const headers = {
    'Authorization': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvaW50ZWdyYXRpb24uYnJlYWRmYXN0LnRlY2giLCJpYXQiOjE3Mjk2MDg1MjAsIm5iZiI6MTcyOTYwODUyMCwiZXhwIjoxODE2MDA4NTIwLCJkYXRhIjp7InVzZXIiOnsiaWQiOiI4NDcwNjYiLCJyb2xlcyI6WyJzdWJfYWRtaW4iXX19fQ.shBfBS1LAHxUfEb0q3HsjE5Vg9iVSoFz12gb4LsQWyo',
    'key': '$2x$bf$.bx5Dh353yA9PQDdFvg4J2UNgvTp3qmxraMf3nT3mW87vVHMg2KXT7',
    'secret': '$0ibd$i9WfIPMqbtSaGrZuKwetJSiGup9kMy4akAwZas7lBt0Xk4zU97uBr.qSjJXW'
  };

  const response = http.get(url, { headers: headers });

  // Check if the request was successful (status code 2xx)
  check(response, {
    'Status is 200': (r) => r.status === 200,
  });
}
