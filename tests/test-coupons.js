import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 4, duration: '30s' },
    { target: 20, duration: '180s' },
    { target: 25, duration: '30s' },
    { target: 75, duration: '120s' },
    { target: 100, duration: '240s' },
    { target: 50, duration: '60s' },
    { target: 25, duration: '180s' },
    { target: 0, duration: '60s' },
  ],
};

export default function () {
  const url = "https://integration.breadfast.tech/wp-json/coupons-api/v1/coupons"; // Fixed URL

  const headers = {
    'Content-Type': 'application/json',
    "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************.tCl3RvMmP3PntvnAsvrHHfILVmxbCNNhSzPhEQCyIjU"
  };

  var ran = Math.random() * 40000;
  const payload = {
    "type": "fixed_cart",
    "coupon_type": "single_code",
    "code": "test0ds0213" + ran,
    "description": "",
    "active": true,
    "coupon_theme": "Commercial",
    "constrains": {
      "usage": {
        "limit_per_coupon": null,
        "max_spend": null,
        "limit_per_user": null,
        "min_spend": null,
        "limit_per_device": null,
        "limit_to_x_item": null,
        "order_limits": {
          "from": null,
          "to": null,
          "limit": null,
          "condition": null
        },
        "last_order_date": {
          "date": null,
          "condition": null
        }
      },
      "general": {
        "fps": [],
        "start_date": "2024-01-18 03:01:45",
        "end_date": "2024-10-26 03:01:57",
        "register_after": null,
        "order_type": "now"
      },
      "roles": {},
      "categories": {},
      "phones": []
    },
    "type_info": {
      "amount": 10
    },
    "bank_id": null,
    "card_type": null
  };

  const response = http.post(url, JSON.stringify(payload), { headers: headers, timeout: "120s" });

  // Check if the HTTP status code is 200
  check(response, {
    'Status is 200': (r) => r.status === 200,
  });
}
