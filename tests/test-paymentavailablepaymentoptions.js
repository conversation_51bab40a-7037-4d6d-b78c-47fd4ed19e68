import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 5, duration: '30s' },
    { target: 10, duration: '60s' },
    { target: 25, duration: '60s' },
    { target: 60, duration: '180s' },
    { target: 100, duration: '240s' },
    { target: 200, duration: '300s' },
    { target: 50, duration: '60s' },
    { target: 30, duration: '120s' },
    { target: 5, duration: '60s' },
  ],
};

export default function() {
  const url = "https://payment-integration.breadfast.tech/api/payment/available-options";

  const headers = {
    'Content-Type': 'application/json',
    "Authorization": "eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.j8_W4xSE4YZ4NdxS2hFhC05-zyUFoE7tQhAEPMr0MaU"
  };

  const payload = {
    "user":{
        "externalId": "38269d0e-4ba3-436a-86e3-afa8af2af8da"
    }
}

  const response = http.get(url, JSON.stringify(payload), { headers: headers, timeout: "120s" });

  let responseData;
  try {
    responseData = response.json();
  } catch (e) {
    console.error("Failed to parse JSON response:", e);
  }

  // Check if the HTTP status code is 200 and the parsed status is also 200
  check(response, {
    'Status is 200': (r) => r.status === 200,
  });

  if (responseData) {
    check(responseData, {
      'Response status is 200': (data) => data.status === 200,
    });
  }
}
