import http from "k6/http";
import { check } from "k6";

const baseUrl = "https://card-backend-preprod.breadfast.tech/api/v1";

export const options = {
  stages: [
    { target: 5, duration: "30s" },
    { target: 10, duration: "60s" },
    { target: 25, duration: "60s" },
    { target: 60, duration: "200s" },
    { target: 150, duration: "300s" },
    { target: 300, duration: "420s" },
    { target: 50, duration: "80s" },
    { target: 30, duration: "140s" },
    { target: 5, duration: "60s" },
  ],
};

const getUserCardDetails = async (token) => {
  const response = await http.post(
    `${baseUrl}/mobile/cards/details`,
    {},
    {
      headers: {
        Authorization: token,
      },
    }
  );
  try {
    const responseData = response.json();
    check(response, {
      "Response status is 200": (r) => r.status === 200,
      "Returns user card details": () => responseData.data !== undefined,
    });
    return responseData;
  } catch (error) {
    console.error(`Failed to get user card details: ${error.message}`);
    throw error;
  }
};

export default function () {
  const userToken =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7Im1vYmlsZV9udW1iZXIiOiIrMjAxMDAwMDAxMDU5IiwidHlwZSI6ImN1c3RvbWVyIiwic2NoZW1lX2lkIjoxLCJpZCI6ImZiYTFmN2VhLWMzODQtNGRjZi04MWE4LTUyZTBjNjkyNTg0MCIsImZuYW1lIjoi2KfZhNi22LrYuCIsImxuYW1lIjoi2KzYr9inIiwiZW1haWwiOiJ0ZXN0MTYxNzM5NzIzNDE1NjU0QGxvYWR0ZXN0LmNvbSJ9LCJpYXQiOjE3NDIxNzE4NDUsImV4cCI6MTc3MzcwNzg0NSwiYXVkIjoicGF5bWUiLCJpc3MiOiJwYXltZSIsImp0aSI6IjI1NTllNmM3LTg2OGMtNDhjZS05ZDUzLTcxMGVkOTI4ZTM0YiJ9.eQMxstIZNkRMkhEEQy3hNWaa8Vj2QMFIXwuHkkXFK1Y";
  try {
    getUserCardDetails(userToken);
    check(null, {
      "User card details request completed": () => true,
    });
  } catch (error) {
    console.error(`Failed to get user card details: ${error.message}`);
    check(null, {
      "User card details request completed": () => false,
    });
  }
}
