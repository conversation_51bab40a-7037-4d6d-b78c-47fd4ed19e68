import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 5, duration: '30s' },
    { target: 10, duration: '60s' },
    { target: 25, duration: '60s' },
    { target: 50, duration: '30s' },
    { target: 75, duration: '100s' },
    { target: 155, duration: '180s' },
    { target: 300, duration: '300s' },
    { target: 50, duration: '60s' },
    { target: 25, duration: '30s' },
  ],
};

export default function() {
  const url = "https://picker-integration.breadfast.tech/control-room/orders?page[limit]=100&page[offset]=0&pending_orders_only=false&fps[]=5e2c373d6b874d02cdc83203&date=22-07-2024";

  const headers = {
    'accept': 'application/json',
    'authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvaW50ZWdyYXRpb24uYnJlYWRmYXN0LnRlY2giLCJpYXQiOjE3MjE2NDU4OTcsIm5iZiI6MTcyMTY0NTg5NywiZXhwIjoxODA4MDQ1ODk3LCJkYXRhIjp7InVzZXIiOnsiaWQiOiI3MjU5Iiwicm9sZXMiOlsiYWRtaW5pc3RyYXRvciIsInN1Yl9hZG1pbiJdfX19.BPqZwHbn8t_vf13m1KAEzBkTyzpjxlbyBqy_v07Qt94',
    'content-type': 'application/json'
  };

  const response = http.get(url, { headers: headers });

  // Check if the request was successful (status code 2xx)
  check(response, {
    'Status is 200': (r) => r.status === 200,
  });
}
