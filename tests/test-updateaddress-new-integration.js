import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 10, duration: '60s' },
    { target: 50, duration: '60s' },
    { target: 75, duration: '120s' },
    { target: 100, duration: '240s' },
    { target: 50, duration: '60s' },
    { target: 25, duration: '60s' },
  ],
};

export default function() {
  const url = "https://new-integration.breadfast.tech/wp-json/breadfast/v3/user/update-address?XDEBUG_SESSION_START=PHPSTORM";

  const headers = {
    'Content-Type': 'application/json',
    "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************.tCl3RvMmP3PntvnAsvrHHfILVmxbCNNhSzPhEQCyIjU"
  };

  const payload = {
    "first_name": "Bishooooys",
    "last_name": "Atiiffs",
    "area": "35299",
    "default_address": "false",
    "phone": "",
    "label": "<PERSON>oome",
    "address": "Maadiiis",
    "location": '{\"lat\":29.9610907,\"lng\":31.2669363,\"address\":\"Side Cairo, Alexandria Desert Road, Ash Sheikh Zayed, Giza, 12577, Egypt\",\"addressLong\":\"Side Cairo, Alexandria Desert Road\",\"label\":\"Side Cairo, Alexandria Desert Road\"}',
    "delivery_instructions": "",
    "flat": "1",
    "floor": "2"
  }

  const response = http.post(url, JSON.stringify(payload), { headers: headers, timeout: "120s" });

  // Check if the request was successful (status code 2xx)
  check(response, {
    'Status is 200': (r) => r.status === 200,
  });
}
