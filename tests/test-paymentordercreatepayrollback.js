import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 5, duration: '30s' },
    { target: 10, duration: '60s' },
    { target: 25, duration: '60s' },
    { target: 60, duration: '180s' },
    { target: 100, duration: '240s' },
    { target: 200, duration: '300s' },
    { target: 50, duration: '60s' },
    { target: 30, duration: '120s' },
    { target: 5, duration: '60s' },
  ],
};


export default function () {
	// First Request: Create the order
	const createOrderUrl = 'https://payment-integration.breadfast.tech/api/order';
	const headers = {
	  'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvaW50ZWdyYXRpb24uYnJlYWRmYXN0LnRlY2giLCJpYXQiOjE3Mjk2MDg1MjAsIm5iZiI6MTcyOTYwODUyMCwiZXhwIjoxODE2MDA4NTIwLCJkYXRhIjp7InVzZXIiOnsiaWQiOiI4NDcwNjYiLCJyb2xlcyI6WyJzdWJfYWRtaW4iXX19fQ.shBfBS1LAHxUfEb0q3HsjE5Vg9iVSoFz12gb4LsQWyo',
	  'key': '$2x$bf$.J8h5zjHg6qLbdcZ9fpYbUehsPVzx/oOrNG1WZRGmNrAjbD3a7WZj2',
	  'secret': '$0ibd$i9WfIPMqbtSaGrZuKwetJSiGup9kMy4akAwZas7lBt0Xk4zU97uBr.qSjJXW',
	  'Content-Type': 'application/json',
	};
  
	const orderPayload = JSON.stringify({
	  amount: 5,
	  currency: "EGP",
	  capture_method: "MANUAL",
	  description: "tmp-payment-load-test",
	  use_balance: false,
	  client_order_id: "PLT-" + new Date().getTime(),
	});

	console.log('Creating order with payload: ' + orderPayload);
	console.log('Request Headers: ' + JSON.stringify(headers));
  
	const orderResponse = http.post(createOrderUrl, orderPayload, { headers: headers });

	console.log('Order Response Status: ' + orderResponse.status);
	console.log('Order Response Body: ' + orderResponse.body);

	check(orderResponse, { 'Order Created - Status 200': (r) => r.status === 200 });
  
	if (orderResponse.status !== 200) {
	  console.log("Order creation failed and actual status code: " + orderResponse.status);
	  return;
	}
  
	// Extract order ID
	const orderId = JSON.parse(orderResponse.body).response.order.id;
	console.log("Order ID: " + orderId);
  
	sleep(10);
  
	// Second Request: Pay for the order
	const payOrderUrl = `https://payment-integration.breadfast.tech/api/order/${orderId}/pay`;
	const payOrderResponse = http.post(payOrderUrl, null, { headers: headers });
	check(payOrderResponse, { 'Order Paid - Status 200': (r) => r.status === 200 });
  
	if (payOrderResponse.status !== 200) {
	  console.log("Payment failed with error code: " + payOrderResponse.status);
	  return;
	}
  
	sleep(10);
  
	// Third Request: Confirm the payment
	const rollbackUrl = `https://payment-integration.breadfast.tech/api/order/${orderId}/rollback`;
	const rollbackResponse = http.post(rollbackUrl, null, { headers: headers });
	check(rollbackResponse, { 'Rolled back - Status 200': (r) => r.status === 200 });
  
	if (rollbackResponse.status !== 200) {
	  console.log("Rollback failed with status code: " + rollbackResponse.status);
	  return;
	}
  
	// Final check to confirm the entire workflow succeeded
	check(true, {
	  'Order Flow Completed Successfully': () => true
	});
  
	console.log("Order flow completed successfully");
  }
  
