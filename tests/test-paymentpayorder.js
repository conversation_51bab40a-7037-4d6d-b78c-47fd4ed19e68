import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 5, duration: '30s' },
    { target: 10, duration: '60s' },
    { target: 25, duration: '60s' },
    { target: 60, duration: '180s' },
    { target: 100, duration: '240s' },
    { target: 200, duration: '300s' },
    { target: 50, duration: '60s' },
    { target: 30, duration: '120s' },
    { target: 5, duration: '60s' },
  ],
};


export default function () {
	// 1st: Create payment order
	const createAPI = 'https://payment-integration.breadfast.tech/api/order';
	const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
	  'Authorization': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvaW50ZWdyYXRpb24uYnJlYWRmYXN0LnRlY2giLCJpYXQiOjE3Mjk2MDg1MjAsIm5iZiI6MTcyOTYwODUyMCwiZXhwIjoxODE2MDA4NTIwLCJkYXRhIjp7InVzZXIiOnsiaWQiOiI4NDcwNjYiLCJyb2xlcyI6WyJzdWJfYWRtaW4iXX19fQ.shBfBS1LAHxUfEb0q3HsjE5Vg9iVSoFz12gb4LsQWyo',
    'key': '$2x$bf$.J8h5zjHg6qLbdcZ9fpYbUehsPVzx/oOrNG1WZRGmNrAjbD3a7WZj2',
    'secret': '$0ibd$i9WfIPMqbtSaGrZuKwetJSiGup9kMy4akAwZas7lBt0Xk4zU97uBr.qSjJXW',
	};
  
	const payload = JSON.stringify({
	  "amount": 5,
    "currency": "EGP",
    "capture_method": "MANUAL",
    "description": "tmp-payment-load-test",
    "use_balance": false,
    "client_order_id": "PLT-" + new Date().getTime()
	});
  
	const createResponse = http.post(createAPI, payload, { headers: headers, timeout: '120s' });
	check(createResponse, {
    'Status is 200': (r) => r.status === 200,
  });
  
	// Extract order ID
	const orderId = JSON.parse(createResponse.body).response.order.id;
	sleep(10);

	// 2nd: pay the order
	const payAPI = `https://payment-integration.breadfast.tech/api/order/${orderId}/pay`;
	const payResponse = http.post(payAPI, null, { headers: headers, timeout: '120s' });
	check(payResponse, {
    'Order Paid - Status 200': (r) => r.status === 200,
  });
  
	if (payResponse.status !== 200) {
	  console.log("Payment failed with error code: " + payResponse.status);
	  return;
	}
	sleep(10);
  
	// 3rd: rollback the order
	const rollbackAPI = `https://payment-integration.breadfast.tech/api/order/${orderId}/rollback`;
	const rollbackResponse = http.post(rollbackAPI, null, { headers: headers, timeout: '120s' });
	check(rollbackResponse, {
    'Rolled back - Status 200': (r) => r.status === 200,
  });
  
	if (rollbackResponse.status !== 200) {
	  console.log("Rollback failed with status code: " + rollbackResponse.status);
	  return;
	}
  
	// Final check to confirm the entire workflow succeeded
	check(true, {
	  'Order Flow Completed Successfully': () => true
	});
  
}
  
