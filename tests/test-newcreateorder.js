import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 5, duration: '30s' },
    { target: 10, duration: '60s' },
    { target: 25, duration: '60s' },
    { target: 60, duration: '180s' },
    { target: 100, duration: '240s' },
    { target: 200, duration: '300s' },
    { target: 50, duration: '60s' },
    { target: 30, duration: '120s' },
    { target: 5, duration: '60s' },
  ],
};

export default function() {
  const url = "https://orders2-0-testing.breadfast.tech/wp-json/breadfast/v3/orders/create"; // Fixed URL

  const headers = {
    'Content-Type': 'application/json',
    "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvbmV3LXRlc3RpbmcuYnJlYWRmYXN0LnRlY2giLCJpYXQiOjE3MzAwNjE4MDEsIm5iZiI6MTczMDA2MTgwMSwiZXhwIjoxODE2NDYxODAxLCJkYXRhIjp7InVzZXIiOnsiaWQiOiI4NjQ1MzkiLCJyb2xlcyI6WyJjdXN0b21lciJdfX19.ipOdj_P5syeOqgd8N8O8uJouWo_0-1RC_VyUUi_PDBY"
  };

  const payload = {
    "address_id": "********",
    "area": "Maadi 2",
    "delivery_date": "9 October, 2024",
    "delivery_time": "06:00 PM - 07:00 PM",
    "delivery_fees": "25",
    "payment_method": "cod",
    "capture_method": "MANUAL",
    "coupon": "{\"success\":true,\"error\":\"\",\"loading\":false,\"code\":\"fcart\",\"amount\":10,\"type\":\"back_to_wallet\",\"products\":{},\"message\":\"You will receive 10 EGP in your balance an hour after receiving your order\",\"id\":\"********\",\"isBankCoupon\":false,\"copounTotalPoints\":37}",
    "products": "{\"81412\": {\"product_id\":81412, \"total\":20, \"subtotal\":20, \"price\":20, \"quantity\":1, \"name\":\"test\", \"name_ar\":\"test\", \"fpPriceEnabled\":true, \"isAutomatedDiscount\":true, \"image\":\"test\"}}",
    "other_data": "{\"platform\":\"ios\", \"gift_receipt\":true, \"dont_use_balance\":true}",
    "warehouse": "{\"warehouseId\":\"5e2c373d6b874d02cdc83203\", \"shiftId\":\"61e83ac63a475c005174f8a8\", \"warehouseName\":\"Maadi FP#1\", \"serve\":\"now\"}"
  };

  const response = http.post(url, JSON.stringify(payload), { headers: headers, timeout: "120s" });

  // Parse the response once
  const responseData = response.json();

  // Check if the HTTP status code is 200 and coupon is applied correctly
  check(response, {
    'Status is 200': (r) => r.status === 200,
  });

  check(responseData, {
    'Coupon is applied': (data) => data.coupon && data.coupon.code === 'fcart',
  });
}
