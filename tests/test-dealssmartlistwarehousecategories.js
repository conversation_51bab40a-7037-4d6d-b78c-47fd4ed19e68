import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 25, duration: '60s' },
    { target: 75, duration: '120s' },
    { target: 125, duration: '180s' },
    { target: 500, duration: '600s' },
    { target: 200, duration: '120s' },
    { target: 50, duration: '60s' },
  ],
};

export default function() {
  const url = getEndpoint()
  const response = http.get(url, { timeout: "120s" });

  // Check if the request was successful (status code 2xx)
  check(response, {
    'Status is 200': (r) => r.status === 200,
  });
}

function getEndpoint() {
  const endpoints = [
    "https://integration.breadfast.tech/warehouses-service/warehouses/categories/2582?serve=now&shiftId=5fc84b891758b02853efc357&warehouseId=658d63b7fc8ee2000d9bca3f&type=deals"
  ]
  const index = Math.floor(Math.random() * endpoints.length);
  return endpoints[index]
}
