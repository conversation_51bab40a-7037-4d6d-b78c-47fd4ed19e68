import http from 'k6/http';
import { check } from 'k6';

export const options = {
    stages: [
        { target: 25, duration: '30s' },
        { target: 75, duration: '100s' },
        { target: 155, duration: '180s' },
        { target: 300, duration: '300s' },
        { target: 50, duration: '60s' },
        { target: 25, duration: '30s' },
    ],
};

export default function() {
    const url = 'https://integration.breadfast.tech/supply-chain/internal-order/transfers/search';

    const body = {
        "warehouseId": "5fc7915b16ef507a34e187f1",
        "skip": 0
    };

    const headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************.tCl3RvMmP3PntvnAsvrHHfILVmxbCNNhSzPhEQCyIjU"
    };

    const response = http.post(url, JSON.stringify(body), { headers: headers, timeout: "120s" });


    check(response, {
        'Status is 200': (r) => r.status === 200,
      });
}
