import http from "k6/http";
import { check } from "k6";

const baseUrl = "https://card-backend-preprod.breadfast.tech/api/v1";

const mobile_numbers = [
  "+201000002317",
  "+201000003331",
  "+201000001884",
  "+201000005860",
  "+201000002454",
  "+201000004221",
  "+201000003391",
  "+201000007008",
  "+201000004311",
  "+201000002012",
  "+201000002011",
  "+201000006097",
  "+201000005744",
  "+201000004964",
  "+201000004878",
  "+201000004554",
  "+201000004782",
  "+201000006200",
  "+201000005628",
  "+201000007078",
  "+201000003358",
  "+201000006320",
  "+201000006083",
  "+201000002772",
  "+201000002239",
  "+201000004450",
  "+201000005155",
  "+201000006600",
  "+201000005563",
  "+201000006857",
  "+201000004654",
  "+201000002298",
  "+201000005358",
  "+201000002345",
  "+201000004080",
  "+201000002561",
  "+201000004146",
  "+201000004044",
  "+201000003604",
  "+201000002900",
  "+201000007020",
  "+201000004665",
  "+201000002046",
  "+201000003703",
  "+201000005055",
  "+201000007006",
  "+201000004443",
  "+201000002679",
  "+201000001868",
  "+201000006559",
  "+201000003670",
  "+201000005881",
  "+201000002702",
  "+201000005491",
  "+201000003871",
  "+201000004225",
  "+201000004143",
  "+201000003729",
  "+201000004142",
  "+201000006751",
  "+201000002052",
  "+201000006909",
  "+201000002262",
  "+201000002337",
  "+201000003656",
  "+201000003058",
  "+201000002117",
  "+201000006971",
  "+201000006367",
  "+201000003076",
  "+201000001878",
  "+201000001861",
  "+201000004476",
  "+201000006679",
  "+201000003802",
  "+201000004227",
  "+201000001976",
  "+201000004199",
  "+201000006310",
  "+201000003503",
  "+201000004478",
  "+201000004233",
  "+201000005017",
  "+201000006270",
  "+201000005542",
  "+201000005223",
  "+201000004280",
  "+201000002251",
  "+201000003028",
  "+201000001847",
  "+201000006737",
  "+201000006564",
  "+201000002799",
  "+201000003654",
  "+201000003469",
  "+201000004056",
  "+201000004849",
  "+201000006986",
  "+201000004532",
  "+201000005249",
  "+201000003569",
  "+201000002166",
  "+201000001821",
  "+201000006807",
  "+201000003895",
  "+201000003499",
  "+201000006147",
  "+201000002898",
  "+201000004925",
  "+201000002197",
  "+201000006271",
  "+201000002945",
  "+201000005674",
  "+201000002661",
  "+201000003655",
  "+201000003444",
  "+201000002828",
  "+201000003456",
  "+201000006465",
  "+201000006378",
  "+201000006714",
  "+201000004580",
  "+201000002541",
  "+201000005589",
  "+201000003511",
  "+201000002617",
  "+201000006163",
  "+201000005229",
  "+201000005053",
  "+201000003971",
  "+201000006821",
  "+201000004666",
  "+201000002355",
  "+201000006099",
  "+201000004653",
  "+201000004455",
  "+201000005093",
  "+201000003298",
  "+201000005469",
  "+201000002103",
  "+201000003383",
  "+201000006268",
  "+201000005339",
  "+201000005909",
  "+201000006008",
  "+201000003681",
  "+201000001944",
  "+201000003283",
  "+201000006006",
  "+201000006452",
  "+201000003912",
  "+201000002339",
  "+201000006238",
  "+201000003994",
  "+201000002233",
  "+201000006519",
  "+201000003955",
  "+201000004336",
  "+201000004019",
  "+201000003630",
  "+201000004488",
  "+201000004055",
  "+201000006774",
  "+201000003171",
  "+201000002387",
  "+201000006211",
  "+201000006553",
  "+201000006641",
  "+201000002604",
  "+201000002641",
  "+201000005254",
  "+201000005099",
  "+201000002083",
  "+201000004914",
  "+201000005797",
  "+201000006401",
  "+201000005553",
  "+201000006581",
  "+201000005265",
  "+201000003351",
  "+201000003719",
  "+201000006916",
  "+201000005214",
  "+201000006005",
  "+201000005253",
  "+201000003731",
  "+201000002400",
  "+201000004449",
  "+201000005058",
  "+201000006773",
  "+201000003233",
  "+201000006634",
  "+201000002190",
  "+201000006557",
  "+201000002093",
  "+201000007068",
  "+201000007034",
  "+201000002676",
  "+201000002579",
  "+201000003891",
  "+201000004521",
  "+201000004208",
  "+201000006711",
  "+201000006366",
  "+201000005288",
  "+201000003959",
  "+201000004133",
  "+201000004619",
  "+201000006184",
  "+201000003095",
  "+201000002698",
  "+201000005886",
  "+201000004445",
  "+201000003043",
  "+201000005112",
  "+201000005264",
  "+201000003737",
  "+201000003385",
  "+201000005322",
  "+201000005150",
  "+201000006297",
  "+201000005321",
  "+201000005484",
  "+201000002643",
  "+201000003014",
  "+201000003187",
  "+201000004993",
  "+201000005404",
  "+201000002599",
  "+201000005625",
  "+201000003887",
  "+201000003127",
  "+201000006126",
  "+201000007009",
  "+201000004543",
  "+201000002448",
  "+201000004419",
  "+201000003951",
  "+201000004730",
  "+201000006287",
  "+201000002831",
  "+201000003559",
  "+201000006703",
  "+201000001882",
  "+201000003597",
  "+201000005375",
  "+201000004176",
  "+201000006542",
  "+201000004347",
  "+201000004078",
  "+201000004661",
  "+201000004431",
  "+201000004294",
  "+201000005350",
  "+201000006098",
  "+201000002263",
  "+201000006931",
  "+201000006322",
  "+201000003889",
  "+201000006789",
  "+201000003633",
  "+201000002440",
  "+201000002189",
  "+201000004393",
  "+201000003969",
  "+201000005514",
  "+201000004966",
  "+201000002667",
  "+201000006839",
  "+201000004769",
  "+201000006556",
  "+201000003550",
  "+201000002567",
  "+201000005700",
  "+201000004215",
  "+201000004830",
  "+201000004664",
  "+201000004096",
  "+201000006709",
  "+201000003027",
  "+201000002612",
  "+201000002979",
  "+201000002740",
  "+201000004773",
  "+201000005175",
  "+201000002378",
  "+201000002362",
  "+201000004021",
  "+201000005580",
  "+201000005009",
  "+201000003181",
  "+201000002328",
  "+201000005710",
  "+201000004154",
  "+201000004552",
  "+201000001888",
  "+201000003328",
  "+201000005696",
  "+201000004216",
  "+201000005013",
  "+201000004659",
  "+201000003823",
  "+201000005990",
  "+201000005535",
  "+201000002706",
  "+201000005458",
  "+201000005655",
  "+201000003585",
  "+201000002479",
  "+201000004171",
  "+201000003078",
  "+201000006106",
  "+201000006282",
  "+201000005134",
  "+201000003780",
  "+201000003563",
  "+201000003276",
  "+201000005726",
  "+201000005961",
  "+201000006620",
  "+201000006740",
  "+201000004628",
  "+201000003777",
  "+201000003288",
  "+201000007062",
  "+201000003705",
  "+201000004149",
  "+201000006332",
  "+201000004318",
  "+201000003330",
  "+201000002127",
  "+201000004497",
  "+201000003497",
  "+201000003183",
  "+201000004390",
  "+201000005090",
  "+201000006731",
  "+201000002553",
  "+201000004923",
  "+201000003090",
  "+201000004774",
  "+201000003312",
  "+201000002653",
  "+201000006800",
  "+201000006687",
  "+201000002219",
  "+201000004868",
  "+201000004235",
  "+201000006246",
  "+201000006571",
  "+201000006267",
  "+201000005779",
  "+201000004346",
  "+201000005304",
  "+201000003204",
  "+201000002882",
  "+201000006188",
  "+201000005671",
  "+201000005502",
  "+201000006070",
  "+201000005111",
  "+201000004669",
  "+201000002885",
  "+201000005389",
  "+201000001803",
  "+201000002456",
  "+201000004745",
  "+201000004466",
  "+201000002982",
  "+201000001995",
  "+201000006754",
  "+201000007005",
  "+201000004670",
  "+201000006582",
  "+201000002978",
  "+201000002670",
  "+201000004754",
  "+201000006155",
  "+201000001954",
  "+201000004967",
  "+201000005815",
  "+201000002257",
  "+201000004092",
  "+201000004684",
  "+201000005668",
  "+201000006921",
  "+201000004094",
  "+201000006862",
  "+201000002255",
  "+201000002232",
  "+201000002761",
  "+201000006016",
  "+201000004620",
  "+201000004841",
  "+201000003113",
  "+201000002490",
  "+201000004303",
  "+201000006275",
  "+201000006867",
  "+201000005063",
  "+201000004051",
  "+201000006868",
  "+201000006748",
  "+201000004103",
  "+201000002950",
  "+201000001900",
  "+201000005634",
  "+201000003855",
  "+201000006276",
  "+201000003478",
  "+201000005683",
  "+201000004129",
  "+201000002026",
  "+201000002913",
  "+201000006024",
  "+201000004655",
  "+201000005987",
  "+201000002349",
  "+201000005557",
  "+201000004671",
  "+201000005693",
  "+201000003045",
  "+201000004361",
  "+201000005702",
  "+201000003338",
  "+201000006403",
  "+201000006700",
  "+201000005051",
  "+201000004337",
  "+201000002651",
  "+201000003537",
  "+201000006461",
  "+201000003632",
  "+201000006265",
  "+201000002451",
  "+201000002372",
  "+201000002609",
  "+201000006303",
  "+201000006970",
  "+201000001858",
  "+201000002585",
  "+201000006389",
  "+201000002404",
  "+201000006107",
  "+201000002276",
  "+201000004836",
  "+201000004981",
  "+201000003316",
  "+201000003220",
  "+201000002347",
  "+201000002308",
  "+201000005397",
  "+201000003225",
  "+201000003023",
  "+201000006908",
  "+201000004102",
  "+201000006818",
  "+201000003578",
  "+201000002226",
  "+201000002056",
  "+201000004273",
  "+201000001856",
  "+201000005202",
  "+201000005626",
  "+201000006761",
  "+201000002234",
  "+201000006300",
  "+201000005348",
  "+201000003882",
  "+201000006408",
  "+201000004240",
  "+201000001938",
  "+201000005325",
  "+201000001909",
  "+201000003000",
  "+201000002215",
  "+201000005517",
  "+201000004750",
  "+201000004787",
  "+201000005667",
  "+201000002043",
  "+201000004748",
  "+201000006243",
  "+201000005080",
  "+201000002153",
  "+201000002879",
  "+201000006783",
  "+201000003650",
  "+201000004138",
  "+201000002994",
  "+201000003907",
  "+201000005629",
  "+201000003355",
  "+201000007080",
  "+201000005707",
  "+201000004954",
  "+201000004738",
  "+201000002447",
  "+201000001936",
  "+201000002506",
  "+201000006314",
  "+201000002888",
  "+201000002920",
  "+201000002147",
  "+201000003663",
  "+201000005045",
  "+201000003044",
  "+201000002723",
  "+201000003577",
  "+201000002111",
  "+201000004018",
  "+201000004260",
  "+201000004679",
  "+201000004965",
  "+201000006100",
  "+201000005638",
  "+201000002911",
  "+201000006939",
  "+201000005043",
  "+201000004887",
  "+201000005522",
  "+201000005685",
  "+201000003239",
  "+201000002940",
  "+201000003517",
  "+201000004998",
  "+201000004170",
  "+201000006618",
  "+201000005020",
  "+201000006847",
  "+201000003952",
  "+201000002505",
  "+201000005882",
  "+201000001881",
  "+201000004598",
  "+201000001815",
  "+201000006577",
  "+201000004904",
  "+201000004975",
  "+201000003083",
  "+201000004622",
  "+201000005070",
  "+201000005440",
  "+201000006023",
  "+201000005989",
  "+201000005143",
  "+201000004909",
  "+201000002373",
  "+201000002821",
  "+201000004551",
  "+201000006874",
  "+201000002361",
  "+201000004302",
  "+201000002554",
  "+201000004286",
  "+201000004112",
  "+201000003034",
  "+201000002060",
  "+201000003254",
  "+201000006376",
  "+201000007073",
  "+201000003224",
  "+201000004211",
  "+201000002225",
  "+201000004434",
  "+201000004274",
  "+201000004626",
  "+201000002169",
  "+201000005930",
  "+201000006030",
  "+201000004493",
  "+201000006697",
  "+201000006380",
  "+201000007014",
  "+201000002822",
  "+201000002192",
  "+201000002495",
  "+201000005107",
  "+201000005867",
  "+201000002097",
  "+201000004625",
  "+201000002659",
  "+201000003243",
  "+201000006938",
  "+201000005911",
  "+201000004486",
  "+201000004469",
  "+201000004940",
  "+201000006313",
  "+201000007039",
  "+201000005510",
  "+201000005235",
  "+201000003481",
  "+201000005312",
  "+201000002085",
];

export const options = {
  stages: [
    { target: 5, duration: "30s" },
    { target: 10, duration: "60s" },
    { target: 25, duration: "60s" },
    { target: 60, duration: "200s" },
    { target: 150, duration: "300s" },
    { target: 250, duration: "420s" },
    { target: 50, duration: "80s" },
    { target: 30, duration: "140s" },
    { target: 5, duration: "60s" },
  ],
};

const loginWalletUser = async (mobile_number) => {
  const response = await http.post(
    `${baseUrl}/mobile/wallet_users/login`,
    JSON.stringify({
      mobile_number: mobile_number,
      mpin: "123654",
      scheme_id: 1,
      device_info: { device_id: "ed83a021-3472-40d5-a262-a17c6d479468" },
    }),
    {
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
  try {
    return response.json().token;
  } catch (error) {
    console.error(`Failed to login wallet user: ${error.message}`);
  }
};

const activateUserCard = async (index) => {
  const mobile_number = mobile_numbers[index];

  const token = await loginWalletUser(mobile_number);
  if (!token) {
    console.error(`Failed to login wallet user: ${mobile_number}`);
    return;
  }
  const response = await http.post(
    `${baseUrl}/mobile/cards/activate`,
    {},
    {
      headers: {
        Authorization: token,
      },
    }
  );
  try {
    const responseData = response.json();
    check(response, {
      "Response status is 200": (r) => r.status === 200,
    });
    return responseData;
  } catch (error) {
    console.error(`Failed to activate card: ${error.message}`);
    throw error;
  }
};

export default function () {
  const index = Math.floor(Math.random() * 590);
  try {
    activateUserCard(index);
    check(null, {
      "Card activation request completed": () => true,
    });
  } catch (error) {
    console.error(`Failed to activate card: ${error.message}`);
    check(null, {
      "Card activation request completed": () => false,
    });
  }
}
