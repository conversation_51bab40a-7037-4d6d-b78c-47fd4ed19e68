import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 5, duration: '30s' },
    { target: 10, duration: '60s' },
    { target: 25, duration: '60s' },
    { target: 60, duration: '180s' },
    { target: 100, duration: '240s' },
    { target: 200, duration: '300s' },
    { target: 50, duration: '60s' },
    { target: 30, duration: '120s' },
    { target: 5, duration: '60s' },
  ],
};

export default function() {
  const url = "https://integration.breadfast.tech/wp-json/breadfast/v3/user/referral";

  const headers = {
    'Content-Type': 'application/json',
    "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvaW50ZWdyYXRpb24uYnJlYWRmYXN0LnRlY2giLCJpYXQiOjE3MjkyNzQwMjEsIm5iZiI6MTcyOTI3NDAyMSwiZXhwIjoxODE1Njc0MDIxLCJkYXRhIjp7InVzZXIiOnsiaWQiOiI4NzU2MjIiLCJyb2xlcyI6WyJjdXN0b21lciIsInN1Yl9hZG1pbiJdfX19.CrLzLPTjR78tLuJcPuJdfPoV1ohT0qJczrQcdpIzerI"
  };

  const payload = {
    'referral_page': 'false'
  };

  const response = http.post(url, JSON.stringify(payload), { headers: headers, timeout: "120s" });

  // Parse the response JSON if possible
  let responseData;
  try {
    responseData = response.json();
  } catch (e) {
    console.error("Failed to parse JSON response:", e);
  }

  // Check if the HTTP status code is 200 and the parsed status is also 200
  check(response, {
    'Status is 200': (r) => r.status === 200,
  });

  if (responseData) {
    check(responseData, {
      'Response status is 200': (data) => data.status === 200,
    });
  }
}
