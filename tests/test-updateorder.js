import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 25, duration: '30s' },
    { target: 75, duration: '120s' },
    { target: 125, duration: '300s' },
    { target: 50, duration: '60s' },
    { target: 25, duration: '30s' },
  ],
};

export default function() {
  const url = "https://integration.breadfast.tech/wp-json/breadfast/v3/hook/inai-callback";

  const headers = {
    'Content-Type': 'text/plain',
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************.tCl3RvMmP3PntvnAsvrHHfILVmxbCNNhSzPhEQCyIjU"
  };

  const payload = {}

  const response = http.post(url, JSON.stringify(payload), { headers: headers, timeout: "120s" });


  console.log(response.status_text)
  // Check if the request was successful (status code 2xx)
  check(response, {
    'Status is 200': (r) => r.status === 200,
  });
}
