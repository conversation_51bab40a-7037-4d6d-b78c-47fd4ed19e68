import http from "k6/http";
import { check } from "k6";

export const options = {
  stages: [
    { target: 5, duration: "30s" },
    { target: 10, duration: "60s" },
    { target: 25, duration: "60s" },
    { target: 60, duration: "200s" },
    { target: 150, duration: "300s" },
    { target: 250, duration: "420s" },
    { target: 50, duration: "80s" },
    { target: 30, duration: "140s" },
    { target: 5, duration: "60s" },
  ],
};

export default function () {
  const baseUrl = "https://card-backend-preprod.breadfast.tech/api/v1";
  const payload = {
    username: "mobile_user",
    password: "Admin@1234567",
  };
  const response = http.post(`${baseUrl}/mobile/user/login`, payload);

  let responseData;

  try {
    responseData = response.json();
  } catch (error) {
    console.error(`Failed to parse JSON response:: ${error.message}`);
  }
  check(response, {
    "Status is 200": (r) => r.status === 200,
  });

  if (responseData) {
    check(responseData, {
      "Response status is 200": (data) => data.token !== null,
    });
  }
}
