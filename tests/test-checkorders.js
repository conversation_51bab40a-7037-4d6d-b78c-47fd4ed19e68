import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { target: 5, duration: '30s' },
    { target: 10, duration: '60s' },
    { target: 25, duration: '60s' },
    { target: 50, duration: '60s' },
    { target: 100, duration: '240s' },
    { target: 200, duration: '200s' },
    { target: 300, duration: '600s' },
    { target: 50, duration: '60s' },
    { target: 30, duration: '120s' },
    { target: 5, duration: '60s' },
  ],
};

export default function () {
  const url =
    'https://integration.breadfast.tech/wp-json/breadfast/v3/user/check-customer-orders';

  const headers = {
    'Content-Type': 'application/json',
    Authorization:
      'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************.tCl3RvMmP3PntvnAsvrHHfILVmxbCNNhSzPhEQCyIjU',
  };

  const payload = {
    address_id: '37377',
    delivery_date: '28 February, 2019',
  };

  const response = http.post(url, JSON.stringify(payload), {
    headers: headers,
    timeout: '120s',
  });

  // Parse the JSON response
  const responseData = response.json();

  // Check if the request was successful (status code 200)
  check(response, {
    'Status is 200': (r) => r.status === 200,
    'Response status is 200': () => responseData.status === 200,
  });
}
