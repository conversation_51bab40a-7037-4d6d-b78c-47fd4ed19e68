### Load Testing Suite

This repository will hold our load testing scenarios, to add a new
load test create a pull request with test scenario similar to the one in
[test-coupons.js](https://github.com/Breadfast/load-testing/blob/406aef3e0716729db5292e31194e2504baa19ea9/tests/test-coupons.js), for now devops team will handle
the kubernetes job to start the test and github workflow.

We are using [k6-operator](https://k6.io/docs/testing-guides/running-distributed-tests/) to run
these tests, it supports a lot of different types of [load testing](https://k6.io/docs/test-types/load-test-types/)
 like soak stress, average load, smoke and spike tests, simple check these
 and create a scenario that is suitable for your endpoints.

You can find the full js API on this page: https://k6.io/docs/javascript-api/

Dashboard for running tests status: https://app.datadoghq.com/dashboard/wmt-8aq-4qe/load-testing

### Important Concepts When Creating Tests

[target and duration](https://k6.io/docs/using-k6/scenarios/executors/ramping-vus/)

The target and duration control how many requests you want to reach at the peak, and how fast, the longer the duration the slower the ramp up will be

*stages*: how many different stages you want the test to run in, usually you should start with a small ramp up and then increase it to the load target in a later stage to reflect real-world behavior

*Virtual Users(vu)*: virtual user will imitate a real user by running the test script, keep in mind
that the number of virtual users won't necessarily correspond to the number of requests, since
a virtual user will wait for the whole script to run before sending a new request, this includes
waiting for a response if the response latency is high.

Test Concepts: https://k6.io/docs/using-k6/scenarios/concepts/

Advanced Examples: https://k6.io/docs/using-k6/scenarios/advanced-examples/

### Testing locally

Before creating a PR, you can check if the test scenario is ok on your local machine using k6 CLI, install it from [here](https://k6.io/docs/get-started/installation/) and then run it using `k6 run script.js`

### Adding a New Test Scenario

Each test have 2 parts: the script that the virtual user will run, and a runner that indicates
the specs of a test run, when creating a new test place the script under the path [tests](/tests),
and the k6 resource under [runners](/runners), the k6 resource is what starts the pods in our cluster that will
do the load test, you can use [k6-coupons.yaml](/runners/k6-coupons.yaml) as a template, keep an eye
on the field parallelism, this controls how many pods will be used to send requests, there is a no hard
number to use since it depends on the requests that you are sending and the responses the test will
have to check, but as a rule of thumb don't send more than 70 requests from the same pod, so for 200 VUs you
want to use `parallelism: 3`, it can be more if the response need a lot of processing.
After that update the github [workflow](/.github/workflows/run.yaml) to add the new test as an option

When adding a new scenario, please follow the conventions of naming the script test-*.js and the runner
k6-*.yaml, the github action is configured to use these prefixes, and try to have something descriptive
of the endpoint you are testing as a suffix, all runners should have the tag `--tag testcase=$test` since this
is the tag we that the datadog dashboard differentiate on for graphing each test case.
